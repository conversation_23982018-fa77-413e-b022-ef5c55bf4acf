/**
 * 测试操作日志API脚本
 */

const http = require('http');

function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });
        
        req.on('error', (err) => {
            reject(err);
        });
        
        if (postData) {
            req.write(postData);
        }
        req.end();
    });
}

async function testActionLogsAPI() {
    try {
        console.log('测试操作日志API...');
        
        // 首先登录获取token
        const loginData = {
            username: 'admin',
            password: 'hn.46.jj'
        };
        
        const loginOptions = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        const loginResponse = await makeRequest(loginOptions, JSON.stringify(loginData));
        
        if (!loginResponse.data.token) {
            console.log('❌ 登录失败:', loginResponse.data.error);
            return;
        }
        
        console.log('✅ 登录成功，获取到token');
        const token = loginResponse.data.token;
        
        // 测试操作日志API
        console.log('\n测试操作日志API...');
        const actionLogsOptions = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/action-logs?limit=10&offset=0',
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        };
        
        const actionLogsResponse = await makeRequest(actionLogsOptions);
        console.log('操作日志API响应状态:', actionLogsResponse.status);
        console.log('操作日志API响应数据:', JSON.stringify(actionLogsResponse.data, null, 2));
        
        if (actionLogsResponse.status === 200 && actionLogsResponse.data.success) {
            console.log('✅ 操作日志API正常工作');
            console.log('日志记录数量:', actionLogsResponse.data.total);
            console.log('返回的日志条数:', actionLogsResponse.data.data.length);
        } else {
            console.log('❌ 操作日志API异常');
        }
        
    } catch (error) {
        console.error('测试失败:', error);
    }
}

// 运行测试
testActionLogsAPI();
