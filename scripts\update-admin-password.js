/**
 * 更新admin用户密码脚本
 */

const bcrypt = require('bcrypt');
const db = require('../config/db');

async function updateAdminPassword() {
    try {
        console.log('开始更新admin用户密码...');
        
        // 新密码
        const newPassword = 'hn.46.jj';
        
        // 生成密码哈希
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        console.log('生成的密码哈希:', hashedPassword);
        
        // 检查admin用户是否存在
        const [existingUsers] = await db.query('SELECT id, username, role FROM users WHERE username = ?', ['admin']);
        
        if (existingUsers.length === 0) {
            // 创建admin用户
            console.log('admin用户不存在，正在创建...');
            const [result] = await db.query(
                'INSERT INTO users (username, password, role) VALUES (?, ?, ?)',
                ['admin', hashedPassword, 'admin']
            );
            console.log('admin用户创建成功，ID:', result.insertId);
        } else {
            // 更新现有admin用户的密码
            console.log('admin用户已存在，正在更新密码...');
            await db.query(
                'UPDATE users SET password = ?, role = ? WHERE username = ?',
                [hashedPassword, 'admin', 'admin']
            );
            console.log('admin用户密码更新成功');
        }
        
        // 验证更新结果
        const [updatedUsers] = await db.query('SELECT id, username, role FROM users WHERE username = ?', ['admin']);
        if (updatedUsers.length > 0) {
            console.log('验证结果:');
            console.log('- 用户ID:', updatedUsers[0].id);
            console.log('- 用户名:', updatedUsers[0].username);
            console.log('- 角色:', updatedUsers[0].role);
            
            // 测试密码验证
            const [testUsers] = await db.query('SELECT password FROM users WHERE username = ?', ['admin']);
            const isPasswordValid = await bcrypt.compare(newPassword, testUsers[0].password);
            console.log('- 密码验证测试:', isPasswordValid ? '通过' : '失败');
        }
        
        console.log('admin用户密码更新完成！');
        console.log('现在可以使用以下凭据登录:');
        console.log('用户名: admin');
        console.log('密码: hn.46.jj');
        
    } catch (error) {
        console.error('更新admin用户密码失败:', error);
    } finally {
        process.exit(0);
    }
}

// 运行脚本
updateAdminPassword();
