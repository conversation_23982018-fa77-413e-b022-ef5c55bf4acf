/**
 * 测试登录功能脚本
 */

const http = require('http');

function makeRequest(options, postData = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({ status: res.statusCode, data: jsonData });
                } catch (e) {
                    resolve({ status: res.statusCode, data: data });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (postData) {
            req.write(postData);
        }
        req.end();
    });
}

async function testLogin() {
    try {
        console.log('测试admin用户登录...');

        const loginData = {
            username: 'admin',
            password: 'hn.46.jj'
        };

        console.log('发送登录请求:', loginData);

        const loginOptions = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        };

        const loginResponse = await makeRequest(loginOptions, JSON.stringify(loginData));
        console.log('响应状态:', loginResponse.status);
        console.log('响应数据:', loginResponse.data);

        if (loginResponse.data.token) {
            console.log('\n✅ 登录成功！');
            console.log('Token:', loginResponse.data.token);
            console.log('用户信息:', loginResponse.data.user);

            // 测试权限检查API
            console.log('\n测试权限检查API...');
            const checkOptions = {
                hostname: 'localhost',
                port: 3001,
                path: '/api/debug/check-role',
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${loginResponse.data.token}`
                }
            };

            const checkResponse = await makeRequest(checkOptions);
            console.log('权限检查结果:', checkResponse.data);

            if (checkResponse.data.status === 'success' && checkResponse.data.user.role === 'admin') {
                console.log('✅ 权限检查通过，用户是管理员');
            } else {
                console.log('❌ 权限检查失败');
            }

        } else {
            console.log('❌ 登录失败:', loginResponse.data.error);
        }

    } catch (error) {
        console.error('测试失败:', error);
    }
}

// 运行测试
testLogin();
